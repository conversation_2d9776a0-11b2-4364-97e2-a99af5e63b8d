import { reserveEquipmentForReservation } from "@/lib/availability";
import { calculateBookingPrice, validateBookingFor<PERSON>reation } from "@/lib/booking-validation";
import { appConfig } from "@/lib/env";
import { generateBookingQRCode, generateVerificationUrl } from "@/lib/qr-code";
import { supabase, getSupabaseAdmin } from "@/lib/supabase";
import { BookingFormData } from "@/lib/types";
import { ServiceOption } from "@/lib/types/services";
import { getServiceOptions, calculateOptionsPricing } from "@/lib/service-options";
import { OptionSelection } from "@/lib/types/service-options";
import { NextRequest, NextResponse } from "next/server";

// Validate service options selections
function validateServiceOptions(selectedOptions: string[], serviceOptions: ServiceOption[]): string[] {
	const errors: string[] = [];

	// If no service options exist, no validation needed
	if (!serviceOptions || serviceOptions.length === 0) {
		// If options are selected but service has no options, that's an error
		if (selectedOptions && selectedOptions.length > 0) {
			errors.push("This service does not have any options available");
		}
		return errors;
	}

	// If no options are selected, only check for required options
	if (!selectedOptions || selectedOptions.length === 0) {
		const requiredOptions = serviceOptions.filter((opt) => opt.required || opt.type === "single_choice_required");
		if (requiredOptions.length > 0) {
			for (const requiredOption of requiredOptions) {
				errors.push(`Option "${requiredOption.name}" is required`);
			}
		}
		return errors;
	}

	// Create a map of options for quick lookup
	const optionsMap = new Map(serviceOptions.map((opt) => [opt.id, opt]));

	// Check if all selected options exist
	for (const optionId of selectedOptions) {
		if (!optionsMap.has(optionId)) {
			errors.push(`Option ${optionId} does not exist for this service`);
		}
	}

	// Check required options
	const requiredOptions = serviceOptions.filter((opt) => opt.required || opt.type === "single_choice_required");
	for (const requiredOption of requiredOptions) {
		if (!selectedOptions.includes(requiredOption.id)) {
			errors.push(`Option "${requiredOption.name}" is required`);
		}
	}

	// Check minimum selections for required_min type
	const requiredMinOptions = serviceOptions.filter((opt) => opt.type === "required_min");
	for (const option of requiredMinOptions) {
		const selectedFromGroup = selectedOptions.filter((id) => id === option.id);
		if (selectedFromGroup.length < (option.min_selections || 1)) {
			errors.push(`Option "${option.name}" requires at least ${option.min_selections || 1} selection(s)`);
		}
	}

	// Check single choice constraints
	const singleChoiceOptions = serviceOptions.filter(
		(opt) => opt.type === "single_choice" || opt.type === "single_choice_required"
	);
	if (singleChoiceOptions.length > 1) {
		const selectedSingleChoice = selectedOptions.filter((id) => singleChoiceOptions.some((opt) => opt.id === id));
		if (selectedSingleChoice.length > 1) {
			errors.push("Only one option can be selected from single choice options");
		}
	}

	return errors;
}

export async function POST(request: NextRequest) {
	try {
		const bookingData: BookingFormData = await request.json();

		// Debug: Log the complete booking data
		console.log("=== BOOKING DEBUG START ===");
		console.log("Environment:", process.env.NODE_ENV);
		console.log("Timezone:", Intl.DateTimeFormat().resolvedOptions().timeZone);
		console.log("Server time:", new Date().toISOString());
		console.log("Received booking data:", JSON.stringify(bookingData, null, 2));
		console.log("TimeSlotId:", bookingData.timeSlotId);
		console.log("TimeSlotId parts:", bookingData.timeSlotId.split("|"));
		console.log("ServiceId:", bookingData.serviceId);
		console.log("Tier Participants:", bookingData.tierParticipants);
		console.log("Customer info:", bookingData.customerInfo);

		// Validate booking data
		console.log("Starting validation...");
		const validation = await validateBookingForCreation(bookingData);
		console.log("Validation result:", {
			isValid: validation.isValid,
			errors: validation.errors,
			warnings: validation.warnings,
		});

		if (!validation.isValid) {
			console.log("=== VALIDATION FAILED ===");
			console.log("Errors:", validation.errors);
			console.log("Warnings:", validation.warnings);
			console.log("=== BOOKING DEBUG END ===");
			return NextResponse.json(
				{
					error: "Validation failed",
					details: validation.errors,
					warnings: validation.warnings,
				},
				{ status: 400 }
			);
		}

		console.log("Validation passed, proceeding with booking creation...");

		// Calculate final pricing using tier participant counts
		const priceCalculation = await calculateBookingPrice(
			bookingData.serviceId,
			bookingData.tierParticipants || [],
			bookingData.selectedOptions,
			bookingData.discountCode
		);

		// Create/update customer - customers table is now independent and self-contained
		// Use admin client for server-side operations
		const adminClient = getSupabaseAdmin();

		// Check if customer exists by email
		const { data: existingCustomer } = await adminClient
			.from("customers")
			.select("*")
			.eq("email", bookingData.customerInfo.email)
			.single();

		let customer;
		if (existingCustomer) {
			// Update existing customer
			const { data: updatedCustomer, error: updateError } = await adminClient
				.from("customers")
				.update({
					first_name: bookingData.customerInfo.firstName,
					last_name: bookingData.customerInfo.lastName,
					phone: bookingData.customerInfo.phone,
					emergency_contact_name: bookingData.customerInfo.emergencyContactName,
					emergency_contact_phone: bookingData.customerInfo.emergencyContactPhone,
				})
				.eq("email", bookingData.customerInfo.email)
				.select()
				.single();

			if (updateError) {
				console.error("Error updating customer:", updateError);
				return NextResponse.json({ error: "Failed to update customer" }, { status: 500 });
			}
			customer = updatedCustomer;
		} else {
			// Create new customer directly
			const customerId = generateUUID();
			console.log("Generated customer ID:", customerId);
			console.log("Creating new independent customer record...");
			console.log("Customer approach: direct customer table insert");

			// Create customer directly with all needed fields
			const { data: newCustomer, error: createError } = await adminClient
				.from("customers")
				.insert({
					id: customerId,
					email: bookingData.customerInfo.email,
					first_name: bookingData.customerInfo.firstName,
					last_name: bookingData.customerInfo.lastName,
					phone: bookingData.customerInfo.phone,
					emergency_contact_name: bookingData.customerInfo.emergencyContactName,
					emergency_contact_phone: bookingData.customerInfo.emergencyContactPhone,
				})
				.select()
				.single();

			if (createError) {
				console.error("Error creating customer:", createError);
				return NextResponse.json({ error: "Failed to create customer record" }, { status: 500 });
			}
			customer = newCustomer;
		}

		// Parse dynamic time slot ID to get start and end times
		const timeSlotParts = bookingData.timeSlotId.split("|");
		const date = timeSlotParts[1];
		const time = timeSlotParts[2];

		// Get service details to calculate end time and for notifications
		const { data: serviceDetails } = await supabase
			.from("services")
			.select("duration_minutes, name, options")
			.eq("id", bookingData.serviceId)
			.single();

		// Create timezone-safe datetime strings that preserve the local time as UTC
		// This matches the format used by the timeslot generation in availability.ts
		const startTimeISO = `${date}T${time}:00.000Z`;
		const startTime = new Date(startTimeISO);
		const endTime = new Date(startTime);
		endTime.setMinutes(endTime.getMinutes() + (serviceDetails?.duration_minutes || 60));
		const endTimeISO = endTime.toISOString();

		// Validate service options using new system
		if (bookingData.selectedOptions && bookingData.selectedOptions.length > 0) {
			try {
				// Get service option assignments
				const assignmentsResponse = await fetch(
					`${process.env.NEXT_PUBLIC_APP_URL}/api/admin/services/${bookingData.serviceId}/option-assignments`
				);
				if (assignmentsResponse.ok) {
					const assignmentsData = await assignmentsResponse.json();
					const assignments = assignmentsData.assignments || [];

					// Get required groups
					const groupsResponse = await fetch(
						`${process.env.NEXT_PUBLIC_APP_URL}/api/admin/services/${bookingData.serviceId}/required-groups`
					);
					const groupsData = groupsResponse.ok ? await groupsResponse.json() : { requiredGroups: [] };
					const requiredGroups = groupsData.requiredGroups || [];

					// Validate option selections
					const { validateOptionSelections } = await import("@/lib/service-options");

					// Convert string array to OptionSelection array if needed
					let optionSelections: OptionSelection[] = [];
					if (Array.isArray(bookingData.selectedOptions)) {
						if (typeof bookingData.selectedOptions[0] === "string") {
							// Convert string array to OptionSelection array
							optionSelections = (bookingData.selectedOptions as string[]).map((optionId) => ({
								optionId,
								quantity: 1,
							}));
						} else {
							// Already OptionSelection array
							optionSelections = bookingData.selectedOptions as OptionSelection[];
						}
					}

					const validation = validateOptionSelections(assignments, optionSelections, requiredGroups);

					if (!validation.isValid) {
						console.log("Option validation errors:", validation.errors);
						return NextResponse.json(
							{
								error: "Sélections d'options invalides",
								details: validation.errors,
							},
							{ status: 400 }
						);
					}

					console.log("Options validation passed:", {
						selectedOptions: bookingData.selectedOptions,
						assignments: assignments.length,
						requiredGroups: requiredGroups.length,
					});
				}
			} catch (error) {
				console.error("Error validating service options:", error);
				// Don't fail the booking for validation errors, just log them
			}
		}

		// Create reservation
		const reservationNumber = generateReservationNumber();

		// Generate a simple QR code identifier (not the full data URL)
		// The actual QR code will be generated on-demand via API
		const qrCodeId = `QR-${reservationNumber}`;

		const reservationData = {
			service_id: bookingData.serviceId,
			customer_id: customer.id,
			reservation_number: reservationNumber,
			start_time: startTimeISO,
			end_time: endTimeISO,
			participant_count: bookingData.tierParticipants?.reduce((sum, tp) => sum + tp.count, 0) || 0,
			total_amount: priceCalculation.total,
			currency: "EUR",
			status: "pending" as const,
			booking_source: "website",
			requires_confirmation: false,
			special_requests: bookingData.specialRequests || null,
			qr_code: qrCodeId, // Simple QR code identifier
			selected_options: bookingData.selectedOptions || [],
			tier_participants: bookingData.tierParticipants || [],
			discount_code: bookingData.discountCode || null,
			discount_amount: priceCalculation.discountAmount || 0,
		};

		const { data: reservation, error: reservationError } = await adminClient
			.from("reservations")
			.insert(reservationData)
			.select()
			.single();

		if (reservationError) {
			console.error("Error creating reservation:", reservationError);
			return NextResponse.json({ error: "Failed to create reservation" }, { status: 500 });
		}

		// Save option selections in the reservations table
		if (
			bookingData.selectedOptions &&
			Array.isArray(bookingData.selectedOptions) &&
			bookingData.selectedOptions.length > 0
		) {
			try {
				// Get service option assignments for pricing
				const assignmentsResponse = await fetch(
					`${process.env.NEXT_PUBLIC_APP_URL}/api/admin/services/${bookingData.serviceId}/option-assignments`
				);
				if (assignmentsResponse.ok) {
					const assignmentsData = await assignmentsResponse.json();
					const assignments = assignmentsData.assignments || [];

					if (assignments.length > 0) {
						// Convert selections to include pricing information
						const optionsWithPricing = bookingData.selectedOptions
							.map((selection: OptionSelection) => {
								const assignment = assignments.find((a: any) => a.option.id === selection.optionId);
								const option = assignment?.option;

								if (option) {
									const unitPrice = option.customPrice ?? option.basePrice;
									return {
										optionId: selection.optionId,
										optionName: option.name,
										quantity: selection.quantity,
										unitPrice: unitPrice,
										totalPrice: unitPrice * selection.quantity,
									};
								}
								return null;
							})
							.filter(Boolean);

						// Update reservation with selected options
						await adminClient
							.from("reservations")
							.update({ selected_options: optionsWithPricing })
							.eq("id", reservation.id);
					}
				}
			} catch (error) {
				console.error("Error saving option selections:", error);
				// Don't fail the booking for option selection errors
			}
		}

		// Reserve equipment
		console.log("Attempting to reserve equipment for reservation:", reservation.id);
		const participantCount = bookingData.tierParticipants?.reduce((sum, tp) => sum + tp.count, 0) || 0;
		const equipmentReserved = await reserveEquipmentForReservation(
			bookingData.serviceId,
			reservation.id,
			participantCount,
			startTime.toISOString(),
			endTime.toISOString()
		);
		console.log("Equipment reservation result:", equipmentReserved);

		if (!equipmentReserved) {
			console.error("Failed to reserve equipment");
			// Rollback reservation
			await adminClient.from("reservations").delete().eq("id", reservation.id);
			return NextResponse.json({ error: "Failed to reserve equipment" }, { status: 500 });
		}

		// Update discount coupon usage if a coupon was applied
		if (bookingData.discountCode && priceCalculation.discountAmount > 0) {
			try {
				console.log("Updating coupon usage for code:", bookingData.discountCode);
				// First get the current usage count
				const { data: currentCoupon } = await adminClient
					.from("discount_coupons")
					.select("current_usage")
					.eq("code", bookingData.discountCode.toUpperCase())
					.eq("is_active", true)
					.single();

				if (currentCoupon) {
					const { error: couponError } = await adminClient
						.from("discount_coupons")
						.update({
							current_usage: (currentCoupon.current_usage || 0) + 1,
							updated_at: new Date().toISOString(),
						})
						.eq("code", bookingData.discountCode.toUpperCase())
						.eq("is_active", true);

					if (couponError) {
						console.error("Error updating coupon usage:", couponError);
					} else {
						console.log("Successfully updated coupon usage");
					}
				}
			} catch (couponUpdateError) {
				console.error("Exception updating coupon usage:", couponUpdateError);
				// Don't fail the booking if coupon update fails
			}
		}

		// Note: With dynamic availability, we don't need to update time slot status
		// Availability is calculated in real-time based on existing reservations

		// Create notification for admin about new booking
		try {
			const { createNotification, createBookingConfirmationNotification } = await import("@/lib/notifications");

			// Get service and customer details for notification
			const { data: service } = await adminClient
				.from("services")
				.select("name")
				.eq("id", bookingData.serviceId)
				.single();

			if (service) {
				const customerName = `${customer.first_name} ${customer.last_name}`;
				const serviceName = service.name;
				const date = new Date(startTime).toLocaleDateString("fr-FR");

				// Get admin users to notify
				const { data: adminProfiles } = await supabase.from("profiles").select("id").eq("role", "admin");

				// Create notification for each admin
				if (adminProfiles) {
					const notificationTemplate = createBookingConfirmationNotification(
						customerName,
						serviceName,
						date,
						reservation.id
					);

					for (const admin of adminProfiles) {
						await createNotification(admin.id, notificationTemplate, reservation.id);
					}
				}
			}
		} catch (notificationError) {
			console.error("Error creating booking notification:", notificationError);
			// Don't fail the booking if notification fails
		}

		// Admin notifications will be sent after payment success

		// Generate QR code for response (not stored in DB)
		const verificationUrl = generateVerificationUrl(reservation.id, appConfig.url);
		const qrCodeDataURL = await generateBookingQRCode({
			reservationId: reservation.id,
			reservationNumber: reservation.reservation_number,
			customerName: `${customer.first_name} ${customer.last_name}`,
			serviceName: serviceDetails?.name || "Service",
			date: date,
			time: time,
			participants: participantCount,
			totalAmount: priceCalculation.total,
			verificationUrl: verificationUrl,
		});

		// Return success response
		console.log("=== BOOKING CREATION SUCCESS ===");
		console.log("Reservation created with ID:", reservation.id);
		console.log("Returning response data:", {
			reservationId: reservation.id,
			qrCode: qrCodeDataURL,
			totalAmount: priceCalculation.total,
			status: "pending",
		});

		return NextResponse.json({
			success: true,
			data: {
				reservationId: reservation.id,
				qrCode: qrCodeDataURL,
				totalAmount: priceCalculation.total,
				status: "pending",
				message: "Réservation créée avec succès",
			},
			warnings: validation.warnings,
		});
	} catch (error) {
		console.error("Error creating booking:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}

export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const customerId = searchParams.get("customer_id");
		const status = searchParams.get("status");
		const limit = parseInt(searchParams.get("limit") || "10");

		try {
			const adminClient = getSupabaseAdmin();
			let query = adminClient
				.from("reservations")
				.select(
					`
        *,
        service:services(name, duration_minutes),
        customer:customers(
          id,
          first_name,
          last_name,
          email,
          phone,
          emergency_contact_name,
          emergency_contact_phone
        )
      `
				)
				.order("created_at", { ascending: false })
				.limit(limit);

			if (customerId) {
				query = query.eq("customer_id", customerId);
			}

			if (status) {
				query = query.eq("status", status);
			}

			const { data: reservations, error } = await query;

			if (error) {
				console.error("Error fetching reservations:", error);
				return NextResponse.json({ error: "Failed to fetch reservations" }, { status: 500 });
			}

			return NextResponse.json({
				success: true,
				data: reservations,
			});
		} catch (error) {
			console.error("Error in GET /api/bookings:", error);
			return NextResponse.json({ error: "Internal server error" }, { status: 500 });
		}
	} catch (error) {
		console.error("Error getting admin client:", error);
		return NextResponse.json({ error: "Database connection not available" }, { status: 500 });
	}
}

// Utility function to generate reservation number
function generateReservationNumber(): string {
	const timestamp = Date.now().toString(36);
	const random = Math.random().toString(36).substring(2, 8);
	return `RES-${timestamp}-${random}`.toUpperCase();
}

// QR code generation is now handled by the generateBookingQRCode function from @/lib/qr-code

// Utility function to generate UUID
function generateUUID(): string {
	// Simple UUID v4 generation
	return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
		const r = (Math.random() * 16) | 0;
		const v = c === "x" ? r : (r & 0x3) | 0x8;
		return v.toString(16);
	});
}
