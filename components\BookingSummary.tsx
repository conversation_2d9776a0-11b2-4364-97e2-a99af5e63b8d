"use client";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ParticipantInfo, PriceCalculation, ServiceWithPricing, TimeSlotWithAvailability } from "@/lib/types";
import { OptionSelection } from "@/lib/types/service-options";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { Calendar, Clock, Euro, MapPin, Users } from "lucide-react";
import { useEffect, useState } from "react";
import { formatTimeFromISO } from "@/lib/time-utils";

interface BookingSummaryProps {
	service: ServiceWithPricing;
	timeSlot: TimeSlotWithAvailability;
	date: Date;
	participants: ParticipantInfo[];
	selectedOptions?: OptionSelection[];
	optionsPrice?: number;
}

export default function BookingSummary({
	service,
	timeSlot,
	date,
	participants,
	selectedOptions = [],
	optionsPrice = 0,
}: BookingSummaryProps) {
	const [priceCalculation, setPriceCalculation] = useState<PriceCalculation | null>(null);
	const [loading, setLoading] = useState(false);

	// Calculate pricing when participants change
	useEffect(() => {
		if (participants.length > 0 && participants.every((p) => p.age > 0)) {
			calculatePricing();
		}
	}, [participants, service.id]);

	const calculatePricing = async () => {
		setLoading(true);
		try {
			// Calculate pricing based on participants and service pricing tiers
			const participantPricing = participants.map((participant) => {
				const tier = service.pricing_tiers.find(
					(tier) =>
						participant.age >= (tier.min_age || 0) &&
						(tier.max_age === null || participant.age <= tier.max_age)
				);

				// Fallback to first tier if no matching tier found
				const fallbackTier = tier || service.pricing_tiers[0];

				return {
					participant,
					tier: fallbackTier,
					price: fallbackTier?.price || 0,
				};
			});

			const subtotal = participantPricing.reduce((sum, item) => sum + item.price, 0);

			setPriceCalculation({
				participants: participantPricing,
				subtotal,
				discountAmount: 0, // TODO: Implement discount logic
				total: subtotal,
			});
		} catch (error) {
			console.error("Error calculating pricing:", error);
		} finally {
			setLoading(false);
		}
	};

	const formatTime = (dateTimeString: string) => {
		return formatTimeFromISO(dateTimeString);
	};

	const formatDate = (date: Date) => {
		return format(date, "EEEE d MMMM yyyy", { locale: fr });
	};

	return (
		<Card className="sticky top-4">
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Euro className="w-5 h-5" />
					Récapitulatif de la réservation
				</CardTitle>
			</CardHeader>

			<CardContent className="space-y-4">
				{/* Service Information */}
				<div className="space-y-3">
					<div>
						<h3 className="font-semibold text-lg">{service.name}</h3>
						<p className="text-sm text-muted-foreground">{service.description}</p>
					</div>

					<div className="flex flex-wrap gap-2">
						<Badge variant="secondary" className="flex items-center gap-1">
							<Clock className="w-3 h-3" />
							{service.duration_minutes} min
						</Badge>
						<Badge variant="secondary" className="flex items-center gap-1">
							<Users className="w-3 h-3" />
							{service.max_participants}
						</Badge>
						<Badge variant="secondary" className="flex items-center gap-1">
							<MapPin className="w-3 h-3" />
							{service.location || "Petit-Canal"}
						</Badge>
					</div>
				</div>

				<Separator />

				{/* Date and Time */}
				<div className="space-y-2">
					<div className="flex items-center gap-2">
						<Calendar className="w-4 h-4 text-muted-foreground" />
						<span className="font-medium">Date et heure</span>
					</div>
					<div className="ml-6 space-y-1">
						<p className="capitalize">{formatDate(date)}</p>
						<p className="text-sm text-muted-foreground">
							{formatTime(timeSlot.start_time)} - {formatTime(timeSlot.end_time)}
						</p>
					</div>
				</div>

				<Separator />

				{/* Participants and Pricing */}
				<div className="space-y-2">
					<div className="flex items-center gap-2">
						<Users className="w-4 h-4 text-muted-foreground" />
						<span className="font-medium">Participants ({participants.length})</span>
					</div>

					{priceCalculation ? (
						<div className="ml-6 space-y-2">
							{priceCalculation.participants.map((item, index) => (
								<div key={index} className="flex justify-between items-center text-sm">
									<div>
										<span className="font-medium">
											{item.participant.firstName || `Participant ${index + 1}`}
										</span>
										<span className="text-muted-foreground ml-2">
											({item.participant.age} ans - {item.tier.tier_name})
										</span>
									</div>
									<span className="font-medium">{item.price}€</span>
								</div>
							))}
						</div>
					) : (
						<div className="ml-6 space-y-2">
							{participants.map((participant, index) => (
								<div key={index} className="flex justify-between items-center text-sm">
									<div>
										<span className="font-medium">
											{participant.firstName || `Participant ${index + 1}`}
										</span>
										<span className="text-muted-foreground ml-2">({participant.age} ans)</span>
									</div>
									<span className="text-muted-foreground">Calcul en cours...</span>
								</div>
							))}
						</div>
					)}
				</div>

				{/* Selected Options */}
				{selectedOptions.length > 0 && (
					<>
						<Separator />
						<div className="space-y-2">
							<div className="flex items-center gap-2">
								<Euro className="w-4 h-4 text-muted-foreground" />
								<span className="font-medium">Options sélectionnées</span>
							</div>
							<div className="ml-6 space-y-2">
								{selectedOptions.map((option, index) => (
									<div key={index} className="flex justify-between text-sm">
										<span className="text-muted-foreground">
											{option.quantity > 1 ? `${option.quantity}x ` : ""}
											Option sélectionnée
										</span>
										<span className="text-muted-foreground">
											{/* We'll need to calculate the price based on option data */}
											Inclus
										</span>
									</div>
								))}
								{optionsPrice > 0 && (
									<div className="flex justify-between text-sm font-medium">
										<span>Total options:</span>
										<span>{optionsPrice.toFixed(2)}€</span>
									</div>
								)}
							</div>
						</div>
					</>
				)}

				{/* Price Breakdown */}
				{priceCalculation && (
					<>
						<Separator />
						<div className="space-y-2">
							<div className="flex justify-between items-center">
								<span>Sous-total</span>
								<span>{priceCalculation.subtotal}€</span>
							</div>

							{priceCalculation.discountAmount > 0 && (
								<div className="flex justify-between items-center text-green-600">
									<span>Réduction</span>
									<span>-{priceCalculation.discountAmount}€</span>
								</div>
							)}

							<Separator />

							<div className="flex justify-between items-center text-lg font-bold">
								<span>Total</span>
								<span>{priceCalculation.total}€</span>
							</div>
						</div>
					</>
				)}

				{/* Capacity Information */}
				<div className="bg-emerald-50 border border-emerald-200 rounded-lg p-3">
					<div className="flex items-center justify-between text-sm">
						<span className="text-emerald-800">Places disponibles</span>
						<span className="font-medium text-emerald-800">{timeSlot.available_capacity} restantes</span>
					</div>
				</div>

				{/* Important Notes */}
				<div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
					<h4 className="font-medium text-blue-800 mb-2">À savoir</h4>
					<ul className="text-xs text-blue-700 space-y-1">
						<li>• Confirmation immédiate par email</li>
						<li>• Annulation gratuite jusqu'à 24h avant</li>
						<li>• Équipement fourni</li>
						<li>• Activité soumise aux conditions météo</li>
					</ul>
				</div>
			</CardContent>
		</Card>
	);
}
