"use client";

import { useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { apiClient } from "@/lib/api-client";

interface OptionSelection {
	id: string;
	option: {
		id: string;
		name: string;
		description?: string;
	};
	group?: {
		id: string;
		name: string;
	};
	quantity: number;
	unitPrice: number;
	totalPrice: number;
}

interface ReservationOptionsDisplayProps {
	reservationId: string;
}

export default function ReservationOptionsDisplay({ reservationId }: ReservationOptionsDisplayProps) {
	const [optionSelections, setOptionSelections] = useState<OptionSelection[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		loadOptionSelections();
	}, [reservationId]);

	const loadOptionSelections = async () => {
		try {
			setLoading(true);
			const data = await apiClient.get(`/api/admin/reservations/${reservationId}`);
			const reservation = data.reservation;

			// Convert selected_options from reservation to the expected format
			if (reservation.selected_options && Array.isArray(reservation.selected_options)) {
				const formattedSelections = reservation.selected_options.map((option: any, index: number) => ({
					id: `option-${index}`,
					option: {
						id: option.optionId,
						name: option.optionName,
						description: null,
					},
					quantity: option.quantity,
					unitPrice: option.unitPrice,
					totalPrice: option.totalPrice,
				}));
				setOptionSelections(formattedSelections);
			} else {
				setOptionSelections([]);
			}
		} catch (error) {
			console.error("Error loading option selections:", error);
			setError("Erreur lors du chargement des options");
		} finally {
			setLoading(false);
		}
	};

	if (loading) {
		return (
			<Card>
				<CardHeader>
					<CardTitle>Options sélectionnées</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="flex items-center justify-center py-4">
						<Loader2 className="h-6 w-6 animate-spin" />
					</div>
				</CardContent>
			</Card>
		);
	}

	if (error) {
		return (
			<Card>
				<CardHeader>
					<CardTitle>Options sélectionnées</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-red-600 text-sm">{error}</div>
				</CardContent>
			</Card>
		);
	}

	if (optionSelections.length === 0) {
		return (
			<Card>
				<CardHeader>
					<CardTitle>Options sélectionnées</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-gray-500 text-sm">Aucune option sélectionnée</div>
				</CardContent>
			</Card>
		);
	}

	const totalOptionsPrice = optionSelections.reduce((sum, selection) => sum + selection.totalPrice, 0);

	return (
		<Card>
			<CardHeader>
				<CardTitle>Options sélectionnées</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="space-y-3">
					{optionSelections.map((selection) => (
						<div key={selection.id} className="flex items-center justify-between p-3 border rounded-lg">
							<div className="flex-1">
								<div className="flex items-center gap-2">
									<h4 className="font-medium text-gray-900">{selection.option.name}</h4>
									{selection.group && (
										<Badge variant="outline" className="text-xs">
											{selection.group.name}
										</Badge>
									)}
								</div>
								{selection.option.description && (
									<p className="text-sm text-gray-600 mt-1">{selection.option.description}</p>
								)}
								{selection.quantity > 1 && (
									<p className="text-sm text-gray-500">
										Quantité: {selection.quantity} × {selection.unitPrice.toFixed(2)}€
									</p>
								)}
							</div>
							<div className="text-right">
								<span className="font-medium text-gray-900">{selection.totalPrice.toFixed(2)}€</span>
							</div>
						</div>
					))}

					{optionSelections.length > 1 && (
						<div className="border-t pt-3 mt-3">
							<div className="flex justify-between items-center">
								<span className="font-medium text-gray-900">Total options:</span>
								<span className="font-semibold text-lg text-gray-900">
									{totalOptionsPrice.toFixed(2)}€
								</span>
							</div>
						</div>
					)}
				</div>
			</CardContent>
		</Card>
	);
}
